import React, { useState } from "react";
import HotelRegistrationWizard from "./components/HotelRegistrationWizard";
import VerificationPanel from "./components/VerificationPanel";
import RegisteredHotelsDisplay from "./components/RegisteredHotelsDisplay";
import "./styles/HotelRegistry.css";

function App() {
  const [activeTab, setActiveTab] = useState('register');
  const [refreshKey, setRefreshKey] = useState(0);

  const handleRegistrationComplete = () => {
    // Refresh the hotels list and switch to view tab
    setRefreshKey(prev => prev + 1);
    setActiveTab('view');
  };

  const tabs = [
    { id: 'register', label: 'Register Hotel', icon: '🏨' },
    { id: 'verify', label: 'Verify Hotels', icon: '✅' },
    { id: 'view', label: 'View Hotels', icon: '📋' }
  ];

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'register':
        return <HotelRegistrationWizard onComplete={handleRegistrationComplete} />;
      case 'verify':
        return <VerificationPanel key={refreshKey} />;
      case 'view':
        return <RegisteredHotelsDisplay key={refreshKey} />;
      default:
        return null;
    }
  };

  return (
    <div className="hotel-registry-container">
      <div className="main-header">
        <p>Hotel Registration & Verification </p>
      </div>

      <div className="nav-tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <span style={{ marginRight: '0.5rem' }}>{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {renderActiveTab()}
    </div>
  );
}

export default App;
