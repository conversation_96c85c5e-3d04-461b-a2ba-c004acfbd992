import React, { useState } from "react";
import Header from "./components/Header";
import Footer from "./components/Footer";
import Home from "./components/Home";
import Booking from "./components/Booking";
import HotelRegistrationWizard from "./components/HotelRegistrationWizard";
import VerificationPanel from "./components/VerificationPanel";
import RegisteredHotelsDisplay from "./components/RegisteredHotelsDisplay";
import "./styles/HotelRegistry.css";

function App() {
  const [activeTab, setActiveTab] = useState('home');
  const [refreshKey, setRefreshKey] = useState(0);

  const handleRegistrationComplete = () => {
    // Refresh the hotels list and switch to hotels tab
    setRefreshKey(prev => prev + 1);
    setActiveTab('hotels');
  };

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'home':
        return <Home setActiveTab={setActiveTab} />;
      case 'booking':
        return <Booking />;
      case 'hotels':
        return <RegisteredHotelsDisplay key={refreshKey} />;
      case 'register':
        return <HotelRegistrationWizard onComplete={handleRegistrationComplete} />;
      case 'verify':
        return <VerificationPanel key={refreshKey} />;
      default:
        return <Home setActiveTab={setActiveTab} />;
    }
  };

  return (
    <div className="app-layout">
      <Header activeTab={activeTab} setActiveTab={setActiveTab} />

      <main className="main-content">
        {renderActiveTab()}
      </main>

      <Footer />
    </div>
  );
}

export default App;
