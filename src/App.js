import React from "react";
import HotelForm from "./components/HotelForm";
import HotelList from "./components/HotelList";

function App() {
  const [refresh, setRefresh] = React.useState(false);

  const handleRegister = () => setRefresh(!refresh);

  return (
      <div style={{ padding: 20 }}>
        <h1>Hotel Registry dApp</h1>
        <HotelForm onRegister={handleRegister} />
          <ErrorBoundary>
              <HotelList />
          </ErrorBoundary>
      </div>
  );
}

export default App;
