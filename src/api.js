import axios from 'axios';

const API = axios.create({
    baseURL: process.env.REACT_APP_API_URL,
});

export const registerHotel = (formData) =>
    API.post("/hotels/register", formData, {
        headers: { "Content-Type": "multipart/form-data" },
    });

export const getHotels = () => API.get("/hotels");

export const verifyHotel = (id) => API.post(`/hotels/verify/${id}`);

export const createRoom = (roomData) => API.post("/rooms", roomData);
export const getRoomsByHotel = (hotelId) => API.get(`/rooms/${hotelId}`);

