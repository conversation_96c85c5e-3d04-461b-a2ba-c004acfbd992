/* Hotel Registry Elegant Styling */

/* Global Layout */
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-light);
}

.main-content {
  flex: 1;
  padding-top: 80px; /* Account for fixed header */
  padding-bottom: 60px; /* Account for footer */
}

/* Header Styles */
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.header-logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--text-white);
  text-decoration: none;
  font-weight: 700;
  font-size: 1.5rem;
  transition: var(--transition-normal);
}

.header-logo:hover {
  transform: translateY(-2px);
  color: var(--accent-light);
}

.logo-icon {
  font-size: 2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.header-nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 2rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-link {
  color: var(--text-white);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  transition: var(--transition-normal);
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.nav-link.active {
  background: var(--accent-color);
  box-shadow: var(--shadow-md);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background: var(--accent-light);
  border-radius: 50%;
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--text-white);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--border-radius);
  transition: var(--transition-normal);
}

.mobile-menu-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Mobile Menu */
.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--primary-color);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-lg);
  z-index: 999;
}

.mobile-menu-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobile-nav-link {
  color: var(--text-white);
  text-decoration: none;
  padding: 1rem;
  border-radius: var(--border-radius);
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  gap: 1rem;
  font-weight: 500;
}

.mobile-nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
}

.mobile-nav-link.active {
  background: var(--accent-color);
}

/* Footer Styles */
.app-footer {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--text-white);
  padding: 3rem 0 1rem;
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3 {
  color: var(--accent-light);
  margin-bottom: 1rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.footer-section p,
.footer-section li {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-link {
  color: var(--text-light);
  text-decoration: none;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0;
}

.footer-link:hover {
  color: var(--accent-light);
  transform: translateX(4px);
}

.footer-social {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: var(--text-white);
  text-decoration: none;
  transition: var(--transition-normal);
}

.social-link:hover {
  background: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
  text-align: center;
  color: var(--text-light);
}

.footer-bottom p {
  margin: 0;
  font-size: 0.875rem;
}

/* Home Page Styles */
.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* Hero Section */
.hero-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  padding: 4rem 0;
  min-height: 60vh;
}

.hero-content {
  max-width: 500px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.brand-highlight {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-card {
  background: white;
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  transform: rotate(5deg);
  transition: var(--transition-normal);
  max-width: 300px;
  width: 100%;
}

.hero-card:hover {
  transform: rotate(0deg) scale(1.05);
}

.hero-card-header {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.hero-card-icon {
  font-size: 2rem;
}

.hero-card-title {
  font-weight: 600;
  font-size: 1.125rem;
}

.hero-card-body {
  padding: 1.5rem;
}

.certificate-badge {
  background: var(--success-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
  display: inline-block;
  margin-bottom: 1rem;
}

.certificate-details p {
  margin: 0.5rem 0;
  color: var(--text-primary);
}

.nft-hash {
  font-family: monospace;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Stats Section */
.stats-section {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  padding: 4rem 0;
  margin: 4rem 0;
  border-radius: var(--border-radius-xl);
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  color: var(--accent-light);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1.125rem;
  opacity: 0.9;
}

/* Features Section */
.features-section {
  padding: 4rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.section-header p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  text-align: center;
  transition: var(--transition-normal);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* How It Works Section */
.how-it-works-section {
  padding: 4rem 0;
  background: var(--bg-light);
}

.steps-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 2rem;
  background: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
}

.step-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.step-number {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.step-content h3 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 1.125rem;
}

.step-content p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  padding: 4rem 0;
  margin: 4rem 0;
  border-radius: var(--border-radius-xl);
  text-align: center;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 2rem;
}

.cta-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.cta-content p {
  font-size: 1.125rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.cta-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Home Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-actions {
    justify-content: center;
  }

  .stats-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .steps-container {
    grid-template-columns: 1fr;
  }

  .step-item {
    flex-direction: column;
    text-align: center;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .stats-container {
    grid-template-columns: 1fr;
  }

  .stat-number {
    font-size: 2rem;
  }
}

/* Booking Page Styles */
.booking-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.booking-header {
  text-align: center;
  margin-bottom: 3rem;
}

.booking-header h1 {
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.booking-header p {
  font-size: 1.25rem;
  color: var(--text-secondary);
}

.booking-search-card {
  margin-bottom: 4rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
}

.booking-search-card .form-label {
  color: white;
  font-weight: 600;
}

.booking-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  align-items: end;
}

.search-button-group {
  display: flex;
  align-items: end;
}

.search-btn {
  width: 100%;
  background: var(--accent-color);
  border: none;
  font-size: 1.125rem;
  padding: 1rem 2rem;
}

.search-btn:hover {
  background: var(--accent-light);
  transform: translateY(-2px);
}

/* Popular Destinations */
.popular-destinations {
  margin-bottom: 4rem;
}

.popular-destinations h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 2rem;
  text-align: center;
}

.destinations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.destination-card {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  cursor: pointer;
}

.destination-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.destination-image {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.destination-info h3 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.destination-info p {
  color: var(--text-secondary);
  margin: 0;
}

/* Booking Features */
.booking-features {
  margin-bottom: 4rem;
}

.booking-features h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 2rem;
  text-align: center;
}

.booking-features .features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature-item {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  text-align: center;
  transition: var(--transition-normal);
}

.feature-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.feature-item .feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.feature-item h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.125rem;
}

.feature-item p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Coming Soon Notice */
.coming-soon-notice {
  background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
  color: white;
  padding: 3rem;
  border-radius: var(--border-radius-xl);
  text-align: center;
}

.notice-content h3 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.notice-content p {
  font-size: 1.125rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.notice-features {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.notice-tag {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
}

:root {
  /* Dark Blue Color Palette */
  --primary-color: #0d3b66;        /* Dark Navy */
  --primary-dark:  #0f172a;         /* Darker Navy */
  --primary-light: #1e293b;        /* Medium Navy */
  --secondary-color: #f8fafc;      /* Light Gray */
  --accent-color: #3b82f6;         /* Bright Blue */
  --accent-light: #60a5fa;         /* Light Blue */
  --success-color: #10b981;        /* Green */
  --danger-color: #ef4444;         /* Red */
  --warning-color: #f59e0b;        /* Amber */
  --text-primary: #1f2937;         /* Dark Gray */
  --text-secondary: #6b7280;       /* Medium Gray */
  --text-light: #9ca3af;           /* Light Gray */
  --text-white: #ffffff;           /* White */
  --border-color: #e5e7eb;         /* Border Gray */
  --bg-light: #f8fafc;             /* Light Background */
  --bg-dark: #0f172a;              /* Dark Background */

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);

  /* Border Radius */
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Main Container */
.hotel-registry-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--bg-light);
  min-height: calc(100vh - 140px); /* Account for header and footer */
}

/* Page Containers - Consistent spacing for all pages */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--bg-light);
  min-height: calc(100vh - 140px);
}

/* Page Headers */
.page-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem 0;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.page-header p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.main-header {
  text-align: center;
  margin-bottom: 3rem;
  color: var(--text-primary);
}

.main-header h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.main-header p {
  font-size: 1.2rem;
  color: var(--text-secondary);
}

/* Navigation */
.nav-tabs {
  display: flex;
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 0.5rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-lg);
}

.nav-tab {
  flex: 1;
  padding: 1rem 2rem;
  background: transparent;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 600;
  color: var(--text-secondary);
  transition: var(--transition-normal);
}

.nav-tab.active {
  background: var(--primary-color);
  color: var(--text-white);
  box-shadow: var(--shadow-md);
}

.nav-tab:hover:not(.active) {
  background: var(--secondary-color);
  color: var(--text-primary);
}

/* Card Styling */
.card {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-lg);
  margin-bottom: 2rem;
}

.card-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--border-color);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

/* Step Indicator */
.step-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
}

.step {
  display: flex;
  align-items: center;
  position: relative;
}

.step:not(:last-child)::after {
  content: '';
  width: 4rem;
  height: 2px;
  background: var(--border-color);
  margin: 0 1rem;
}

.step.completed:not(:last-child)::after {
  background: var(--accent-color);
}

.step-circle {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: var(--border-color);
  color: black;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.step.active .step-circle {
  background: var(--border-color);
  color: black;
}

.step.completed .step-circle {
  background: var(--accent-color);
  color: white;
}

.step-label {
  position: absolute;
  top: 3.5rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  white-space: nowrap;
}

.step.active .step-label {
  color: var(--primary-color);
  font-weight: 600;
}

/* Form Styling */
.form-group {
  margin-bottom: 1.5rem;
  width: fit-content;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-input.error {
  border-color: var(--danger-color);
}

.form-error {
  color: var(--danger-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* File Upload */
.file-upload {
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.file-upload:hover {
  border-color: var(--primary-color);
  background: rgba(37, 99, 235, 0.05);
}

.file-upload.dragover {
  border-color: var(--primary-color);
  background: rgba(37, 99, 235, 0.1);
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.btn-primary {
  background: var(--primary-color);
  color: var(--text-white);
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--secondary-color);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
}

.btn-secondary:hover {
  background: white;
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-success {
  background: var(--success-color);
  color: var(--text-white);
}

.btn-success:hover {
  background: #059669;
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Button Groups */
.btn-group {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

/* Room Management */
.room-list {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

.room-item {
  background: var(--secondary-color);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.room-info h4 {
  margin: 0 0 0.25rem 0;
  color: var(--text-primary);
}

.room-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.room-actions {
  display: flex;
  gap: 0.5rem;
}

/* Hotel Grid */
.hotel-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.hotel-card {
  background: white;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.hotel-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.hotel-card-header {
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--text-white);
}

.hotel-card-body {
  padding: 1.5rem;
}

.hotel-status {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
}

.hotel-status.verified {
  background: rgba(16, 185, 129, 0.1);
  color: var(--accent-color);
}

.hotel-status.pending {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-container {
    padding: 0 1rem;
  }

  .nav-links {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .main-content {
    padding-top: 70px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .footer-social {
    justify-content: center;
  }

  .hotel-registry-container,
  .page-container {
    padding: 1rem;
  }

  .main-header h1,
  .page-header h1 {
    font-size: 2rem;
  }

  .page-header {
    padding: 1rem 0;
    margin-bottom: 2rem;
  }

  .nav-tabs {
    flex-direction: column;
    gap: 0.5rem;
  }

  .step-indicator {
    flex-direction: column;
    gap: 1rem;
  }

  .step:not(:last-child)::after {
    display: none;
  }

  .hotel-grid {
    grid-template-columns: 1fr;
  }

  .btn-group {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .header-logo {
    font-size: 1.25rem;
  }

  .logo-icon {
    font-size: 1.5rem;
  }

  .header-container {
    height: 70px;
  }
}

/* Loading States */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}
