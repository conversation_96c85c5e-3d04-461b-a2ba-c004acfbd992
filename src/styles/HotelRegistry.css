/* Hotel Registry Elegant Styling */

:root {
  --primary-color: #03045e;
  --primary-dark: #1b5886;
  --secondary-color: #f8fafc;
  --accent-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --border-radius: 8px;
  --border-radius-lg: 12px;
}

/* Main Container */
.hotel-registry-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: white;
  min-height: 100vh;
}

.main-header {
  text-align: center;
  margin-bottom: 3rem;
  color: white;
}

.main-header h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.main-header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* Navigation */
.nav-tabs {
  display: flex;
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 0.5rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-lg);
}

.nav-tab {
  flex: 1;
  padding: 1rem 2rem;
  background: transparent;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 600;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.nav-tab.active {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-md);
}

.nav-tab:hover:not(.active) {
  background: var(--secondary-color);
  color: var(--text-primary);
}

/* Card Styling */
.card {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-lg);
  margin-bottom: 2rem;
}

.card-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--border-color);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

/* Step Indicator */
.step-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
}

.step {
  display: flex;
  align-items: center;
  position: relative;
}

.step:not(:last-child)::after {
  content: '';
  width: 4rem;
  height: 2px;
  background: var(--border-color);
  margin: 0 1rem;
}

.step.completed:not(:last-child)::after {
  background: var(--accent-color);
}

.step-circle {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: var(--border-color);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.step.active .step-circle {
  background: var(--secondary-color);
  color: white;
}

.step.completed .step-circle {
  background: var(--accent-color);
  color: white;
}

.step-label {
  position: absolute;
  top: 3.5rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  white-space: nowrap;
}

.step.active .step-label {
  color: var(--primary-color);
  font-weight: 600;
}

/* Form Styling */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-input.error {
  border-color: var(--danger-color);
}

.form-error {
  color: var(--danger-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* File Upload */
.file-upload {
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.file-upload:hover {
  border-color: var(--primary-color);
  background: rgba(37, 99, 235, 0.05);
}

.file-upload.dragover {
  border-color: var(--primary-color);
  background: rgba(37, 99, 235, 0.1);
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--secondary-color);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
}

.btn-secondary:hover {
  background: white;
  border-color: var(--primary-color);
}

.btn-success {
  background: var(--accent-color);
  color: white;
}

.btn-success:hover {
  background: #059669;
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Button Groups */
.btn-group {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

/* Room Management */
.room-list {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

.room-item {
  background: var(--secondary-color);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.room-info h4 {
  margin: 0 0 0.25rem 0;
  color: var(--text-primary);
}

.room-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.room-actions {
  display: flex;
  gap: 0.5rem;
}

/* Hotel Grid */
.hotel-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.hotel-card {
  background: white;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.hotel-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.hotel-card-header {
  padding: 1.5rem;
  background: var(--primary-dark);
  color: white;
}

.hotel-card-body {
  padding: 1.5rem;
}

.hotel-status {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
}

.hotel-status.verified {
  background: rgba(16, 185, 129, 0.1);
  color: #1d4ed8
}

.hotel-status.pending {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hotel-registry-container {
    padding: 1rem;
  }
  
  .main-header h1 {
    font-size: 2rem;
  }
  
  .nav-tabs {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .step-indicator {
    flex-direction: column;
    gap: 1rem;
  }
  
  .step:not(:last-child)::after {
    display: none;
  }
  
  .hotel-grid {
    grid-template-columns: 1fr;
  }
  
  .btn-group {
    flex-direction: column;
  }
}

/* Loading States */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}
