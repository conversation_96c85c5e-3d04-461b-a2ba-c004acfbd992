import React, { useEffect, useState } from "react";
import { getHotels, verifyHotel } from "../api";

const HotelList = () => {
    const [hotels, setHotels] = useState([]);

    const fetchHotels = async () => {
        try {
            const res = await getHotels();
            setHotels(Array.isArray(res.data) ? res.data : []);
        } catch (err) {
            setHotels([]);
        }
    };

    const handleVerify = async (id) => {
        try {
            const res = await verifyHotel(id);
            alert("Hotel verified! TxHash: " + res.data.txHash);
            fetchHotels();
        } catch (err) {
            console.error(err);
            alert("Verification failed");
        }
    };

    useEffect(() => {
        fetchHotels();
    }, []);

    return (
        <div>
            <h2>Registered Hotels</h2>
            <ul>
                {hotels.map((hotel) => (
                    <li key={hotel._id}>
                        <strong>{hotel.name}</strong> - {hotel.location} <br />
                        Wallet: {hotel.walletAddress} <br />
                        License: <a href={`https://ipfs.io/ipfs/${hotel.licenseIPFSHash}`} target="_blank" rel="noreferrer">View File</a><br />
                        Verified: {hotel.isVerified ? "✅" : "❌"}
                        {hotel.nftTxHash && (
                            <>
                                <br />Tx: <a href={`https://preprod.cardanoscan.io/transaction/${hotel.nftTxHash}`} target="_blank" rel="noreferrer">{hotel.nftTxHash.slice(0, 10)}...</a>
                            </>
                        )}
                        {!hotel.isVerified && (
                            <div>
                                <button onClick={() => handleVerify(hotel._id)}>Verify & Mint NFT</button>
                            </div>
                        )}
                        <hr />
                    </li>
                ))}
            </ul>
        </div>
    );
};

export default HotelList;