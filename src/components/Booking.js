import React, { useState } from 'react';
import '../styles/HotelRegistry.css';

const Booking = () => {
  const [bookingData, setBookingData] = useState({
    checkIn: '',
    checkOut: '',
    guests: 1,
    roomType: '',
    location: ''
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setBookingData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSearch = (e) => {
    e.preventDefault();
    // TODO: Implement booking search functionality
    alert('Booking search functionality will be implemented with backend integration!');
  };

  const popularDestinations = [
    { name: 'Colombo', image: '🏙️', hotels: 45 },
    { name: 'Kandy', image: '🏔️', hotels: 32 },
    { name: 'Galle', image: '🏖️', hotels: 28 },
    { name: 'Negombo', image: '🌊', hotels: 22 },
    { name: 'Ella', image: '🌿', hotels: 18 },
    { name: 'Nuwara Eliya', image: '☁️', hotels: 15 }
  ];

  const roomTypes = [
    'Standard Room',
    'Deluxe Room',
    'Suite',
    'Executive Suite',
    'Presidential Suite',
    'Family Room'
  ];

  return (
    <div className="booking-container">
      {/* Booking Header */}
      <div className="booking-header">
        <h1>🏨 Book Your Perfect Stay</h1>
        <p>Find and book verified hotels with blockchain-secured reservations</p>
      </div>

      {/* Booking Search Form */}
      <div className="card booking-search-card">
        <form onSubmit={handleSearch} className="booking-form">
          <div className="booking-form-grid">
            <div className="form-group">
              <label className="form-label">📍 Location</label>
              <input
                type="text"
                name="location"
                value={bookingData.location}
                onChange={handleChange}
                className="form-input"
                placeholder="Where do you want to stay?"
                required
              />
            </div>

            <div className="form-group">
              <label className="form-label">📅 Check-in Date</label>
              <input
                type="date"
                name="checkIn"
                value={bookingData.checkIn}
                onChange={handleChange}
                className="form-input"
                required
              />
            </div>

            <div className="form-group">
              <label className="form-label">📅 Check-out Date</label>
              <input
                type="date"
                name="checkOut"
                value={bookingData.checkOut}
                onChange={handleChange}
                className="form-input"
                required
              />
            </div>

            <div className="form-group">
              <label className="form-label">👥 Guests</label>
              <select
                name="guests"
                value={bookingData.guests}
                onChange={handleChange}
                className="form-input"
              >
                {[1, 2, 3, 4, 5, 6].map(num => (
                  <option key={num} value={num}>
                    {num} Guest{num > 1 ? 's' : ''}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">🛏️ Room Type</label>
              <select
                name="roomType"
                value={bookingData.roomType}
                onChange={handleChange}
                className="form-input"
              >
                <option value="">Any Room Type</option>
                {roomTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            <div className="form-group search-button-group">
              <button type="submit" className="btn btn-primary btn-lg search-btn">
                🔍 Search Hotels
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* Popular Destinations */}
      <div className="popular-destinations">
        <h2>🌟 Popular Destinations</h2>
        <div className="destinations-grid">
          {popularDestinations.map((destination, index) => (
            <div key={index} className="destination-card">
              <div className="destination-image">{destination.image}</div>
              <div className="destination-info">
                <h3>{destination.name}</h3>
                <p>{destination.hotels} verified hotels</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Features */}
      <div className="booking-features">
        <h2>🔒 Why Book with TravelOK?</h2>
        <div className="features-grid">
          <div className="feature-item">
            <div className="feature-icon">⛓️</div>
            <h3>Blockchain Verified</h3>
            <p>All hotels are verified on the Cardano blockchain for authenticity</p>
          </div>
          <div className="feature-item">
            <div className="feature-icon">🔐</div>
            <h3>Secure Payments</h3>
            <p>Cryptocurrency and traditional payment methods supported</p>
          </div>
          <div className="feature-item">
            <div className="feature-icon">🎨</div>
            <h3>NFT Receipts</h3>
            <p>Get unique NFT receipts for your bookings as proof of stay</p>
          </div>
          <div className="feature-item">
            <div className="feature-icon">⚡</div>
            <h3>Instant Confirmation</h3>
            <p>Real-time booking confirmation with smart contracts</p>
          </div>
        </div>
      </div>

      {/* Coming Soon Notice */}
      <div className="coming-soon-notice">
        <div className="notice-content">
          <h3>🚀 Coming Soon!</h3>
          <p>
            Full booking functionality is currently under development. 
            Soon you'll be able to book rooms directly through our platform 
            with cryptocurrency payments and NFT receipts!
          </p>
          <div className="notice-features">
            <span className="notice-tag">💳 Crypto Payments</span>
            <span className="notice-tag">🎨 NFT Receipts</span>
            <span className="notice-tag">⚡ Smart Contracts</span>
            <span className="notice-tag">🔒 Secure Escrow</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Booking;
