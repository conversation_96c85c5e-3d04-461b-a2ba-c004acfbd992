import React, { useState } from 'react';
import '../styles/HotelRegistry.css';

const HotelBasicInfo = ({ formData, setFormData, onNext }) => {
  const [errors, setErrors] = useState({});
  const [dragOver, setDragOver] = useState(false);

  const handleChange = (e) => {
    const { name, value, files } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: files ? files[0] : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      setFormData(prev => ({ ...prev, license: files[0] }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name?.trim()) {
      newErrors.name = 'Hotel name is required';
    }
    
    if (!formData.location?.trim()) {
      newErrors.location = 'Location is required';
    }
    
    if (!formData.walletAddress?.trim()) {
      newErrors.walletAddress = 'Wallet address is required';
    } else if (!/^addr[a-z0-9]{98}$/.test(formData.walletAddress.trim())) {
      newErrors.walletAddress = 'Please enter a valid Cardano wallet address';
    }
    
    if (!formData.license) {
      newErrors.license = 'License document is required';
    } else {
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];
      if (!allowedTypes.includes(formData.license.type)) {
        newErrors.license = 'Please upload a PDF, JPG, or PNG file';
      }
      if (formData.license.size > 10 * 1024 * 1024) { // 10MB limit
        newErrors.license = 'File size must be less than 10MB';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onNext();
    }
  };

  return (
    <div className="card fade-in">
      <div className="card-header">
        <h2 className="card-title">Hotel Information</h2>
        <p>Please provide basic information about your hotel</p>
      </div>
      
      <div className="form-group">
        <label className="form-label">Hotel Name *</label>
        <input
          type="text"
          name="name"
          value={formData.name || ''}
          onChange={handleChange}
          className={`form-input ${errors.name ? 'error' : ''}`}
          placeholder="Enter your hotel name"
        />
        {errors.name && <div className="form-error">{errors.name}</div>}
      </div>

      <div className="form-group">
        <label className="form-label">Location *</label>
        <input
          type="text"
          name="location"
          value={formData.location || ''}
          onChange={handleChange}
          className={`form-input ${errors.location ? 'error' : ''}`}
          placeholder="Enter hotel location (e.g., Colombo, Sri Lanka)"
        />
        {errors.location && <div className="form-error">{errors.location}</div>}
      </div>

      <div className="form-group">
        <label className="form-label">Wallet Address *</label>
        <input
          type="text"
          name="walletAddress"
          value={formData.walletAddress || ''}
          onChange={handleChange}
          className={`form-input ${errors.walletAddress ? 'error' : ''}`}
          placeholder="addr1..."
        />
        {errors.walletAddress && <div className="form-error">{errors.walletAddress}</div>}
        <small style={{ color: '#6b7280', fontSize: '0.875rem' }}>
          This will be used for NFT minting and verification
        </small>
      </div>

      <div className="form-group">
        <label className="form-label">Business License *</label>
        <div 
          className={`file-upload ${dragOver ? 'dragover' : ''} ${errors.license ? 'error' : ''}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => document.getElementById('license-input').click()}
        >
          <input
            id="license-input"
            type="file"
            name="license"
            accept=".pdf,.jpg,.jpeg,.png"
            onChange={handleChange}
            style={{ display: 'none' }}
          />
          <div>
            {formData.license ? (
              <div>
                <p style={{ margin: 0, fontWeight: '600', color: '#10b981' }}>
                  ✓ {formData.license.name}
                </p>
                <p style={{ margin: '0.25rem 0 0 0', fontSize: '0.875rem', color: '#6b7280' }}>
                  {(formData.license.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            ) : (
              <div>
                <p style={{ margin: 0, fontWeight: '600' }}>
                  📄 Drop your license file here or click to browse
                </p>
                <p style={{ margin: '0.25rem 0 0 0', fontSize: '0.875rem', color: '#6b7280' }}>
                  Supports PDF, JPG, PNG (max 10MB)
                </p>
              </div>
            )}
          </div>
        </div>
        {errors.license && <div className="form-error">{errors.license}</div>}
      </div>

      <div className="btn-group">
        <button 
          type="button" 
          className="btn btn-primary btn-lg"
          onClick={handleNext}
        >
          Next: Add Rooms →
        </button>
      </div>
    </div>
  );
};

export default HotelBasicInfo;
