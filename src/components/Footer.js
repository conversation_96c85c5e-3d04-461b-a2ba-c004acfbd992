import React from 'react';
import '../styles/HotelRegistry.css';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="app-footer">
      <div className="footer-container">
        <div className="footer-content">
          {/* Company Info */}
          <div className="footer-section">
            <h3>🏨 TravelOK</h3>
            <p>
              Revolutionizing hotel registration and verification through blockchain technology. 
              Secure, transparent, and decentralized hotel management platform.
            </p>
            <div className="footer-social">
              <a href="#" className="social-link" aria-label="Twitter">
                🐦
              </a>
              <a href="#" className="social-link" aria-label="LinkedIn">
                💼
              </a>
              <a href="#" className="social-link" aria-label="GitHub">
                🐙
              </a>
              <a href="#" className="social-link" aria-label="Discord">
                💬
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="footer-section">
            <h3>Quick Links</h3>
            <ul>
              <li>
                <a href="#" className="footer-link">
                  <span>🏠</span>
                  <span>Home</span>
                </a>
              </li>
              <li>
                <a href="#" className="footer-link">
                  <span>🏨</span>
                  <span>Browse Hotels</span>
                </a>
              </li>
              <li>
                <a href="#" className="footer-link">
                  <span>📝</span>
                  <span>Register Hotel</span>
                </a>
              </li>
              <li>
                <a href="#" className="footer-link">
                  <span>📅</span>
                  <span>Book Now</span>
                </a>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div className="footer-section">
            <h3>Support</h3>
            <ul>
              <li>
                <a href="#" className="footer-link">
                  <span>❓</span>
                  <span>Help Center</span>
                </a>
              </li>
              <li>
                <a href="#" className="footer-link">
                  <span>📞</span>
                  <span>Contact Us</span>
                </a>
              </li>
              <li>
                <a href="#" className="footer-link">
                  <span>📋</span>
                  <span>Documentation</span>
                </a>
              </li>
              <li>
                <a href="#" className="footer-link">
                  <span>🔒</span>
                  <span>Privacy Policy</span>
                </a>
              </li>
            </ul>
          </div>

          {/* Technology */}
          <div className="footer-section">
            <h3>Technology</h3>
            <ul>
              <li>
                <a href="#" className="footer-link">
                  <span>⛓️</span>
                  <span>Cardano Blockchain</span>
                </a>
              </li>
              <li>
                <a href="#" className="footer-link">
                  <span>🌐</span>
                  <span>IPFS Storage</span>
                </a>
              </li>
              <li>
                <a href="#" className="footer-link">
                  <span>🎨</span>
                  <span>NFT Certificates</span>
                </a>
              </li>
              <li>
                <a href="#" className="footer-link">
                  <span>🔐</span>
                  <span>Smart Contracts</span>
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="footer-bottom">
          <p>
            © {currentYear} TravelOK. All rights reserved. | 
            Built with ❤️ on Cardano | 
            Powered by React & Blockchain Technology
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
