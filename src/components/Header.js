import React, { useState } from 'react';
import '../styles/HotelRegistry.css';

const Header = ({ activeTab, setActiveTab }) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const navigationItems = [
    { id: 'home', label: 'Home', icon: '🏠' },
    { id: 'booking', label: 'Booking', icon: '📅' },
    { id: 'hotels', label: 'Hotels', icon: '🏨' },
    { id: 'register', label: 'Register', icon: '📝' },
    { id: 'verify', label: 'Verify', icon: '✅' }
  ];

  const handleNavClick = (tabId) => {
    setActiveTab(tabId);
    setMobileMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <header className="app-header">
      <div className="header-container">
        {/* Logo */}
        <a href="#" className="header-logo" onClick={(e) => { e.preventDefault(); handleNavClick('home'); }}>
          <span className="logo-icon">🏨</span>
          <span>TravelOK</span>
        </a>

        {/* Desktop Navigation */}
        <nav className="header-nav">
          <ul className="nav-links">
            {navigationItems.map((item) => (
              <li key={item.id}>
                <a
                  href="#"
                  className={`nav-link ${activeTab === item.id ? 'active' : ''}`}
                  onClick={(e) => {
                    e.preventDefault();
                    handleNavClick(item.id);
                  }}
                >
                  <span>{item.icon}</span>
                  <span>{item.label}</span>
                </a>
              </li>
            ))}
          </ul>

          {/* Mobile Menu Toggle */}
          <button 
            className="mobile-menu-toggle"
            onClick={toggleMobileMenu}
            aria-label="Toggle mobile menu"
          >
            {mobileMenuOpen ? '✕' : '☰'}
          </button>
        </nav>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="mobile-menu">
          <div className="mobile-menu-content">
            {navigationItems.map((item) => (
              <a
                key={item.id}
                href="#"
                className={`mobile-nav-link ${activeTab === item.id ? 'active' : ''}`}
                onClick={(e) => {
                  e.preventDefault();
                  handleNavClick(item.id);
                }}
              >
                <span>{item.icon}</span>
                <span>{item.label}</span>
              </a>
            ))}
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
