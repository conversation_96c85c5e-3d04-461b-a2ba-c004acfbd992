import React, { useState } from "react";
import { registerHotel } from "../api";

const HotelForm = ({ onRegister }) => {
    const [formData, setFormData] = useState({
        name: "",
        location: "",
        walletAddress: "",
        license: null,
    });

    const handleChange = (e) => {
        const { name, value, files } = e.target;
        setFormData({ ...formData, [name]: files ? files[0] : value });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        const data = new FormData();
        data.append("name", formData.name);
        data.append("location", formData.location);
        data.append("walletAddress", formData.walletAddress);
        data.append("license", formData.license);



        try {
            await registerHotel(data);
            alert("Hotel registered successfully!");
            onRegister(); // refresh hotel list
        } catch (err) {
            console.error(err);
            alert("Registration failed");
        }
    };

    return (
        <form onSubmit={handleSubmit}>
            <h2>Register New Hotel</h2>
            <input type="text" name="name" placeholder="Hotel Name" onChange={handleChange} required />
            <input type="text" name="location" placeholder="Location" onChange={handleChange} required />
            <input type="text" name="walletAddress" placeholder="Wallet Address" onChange={handleChange} required />
            <input type="file" name="license" accept=".pdf,.jpg,.png" onChange={handleChange} required />
            <button type="submit">Register Hotel</button>
        </form>

    <MapContainer center={[7.8731, 80.7718]} zoom={7}>
        <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
        {hotels.map(h => (
            <Marker key={h._id} position={[h.geo.lat, h.geo.lng]}>
                <Popup>{h.name} - {h.location}</Popup>
            </Marker>
        ))}
    </MapContainer>

);
};

export default HotelForm;
