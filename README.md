# 🏨 Hotel Registry Frontend

An elegant, modern frontend for the decentralized hotel registration and verification platform built with React.

## ✨ Features

### 🎯 Multi-Step Hotel Registration
- **Step 1: Hotel Information** - Basic hotel details, location, wallet address, and license upload
- **Step 2: Room Management** - Add multiple room types with descriptions and pricing
- **Step 3: Review & Submit** - Comprehensive summary before final submission

### 🔐 Admin Verification Panel
- Review pending hotel registrations
- Verify hotels and mint NFTs
- View detailed hotel and room information
- Track verification status and transaction hashes

### 📋 Registered Hotels Display
- Browse all registered hotels
- Filter by verification status
- Search by name or location
- View detailed hotel information and rooms
- Access IPFS documents and blockchain transactions

### 🎨 Modern UI/UX
- Elegant gradient backgrounds and modern card designs
- Responsive design for all screen sizes
- Smooth animations and transitions
- Intuitive step-by-step wizard interface
- Professional color scheme and typography

## 🚀 Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn
- Backend API server running

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd hotel-registry-frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   ```

   Update `.env` with your backend API URL:
   ```
   REACT_APP_API_URL=http://localhost:5000/api
   ```

4. **Start the development server**
   ```bash
   npm start
   ```

   The app will open at [http://localhost:3000](http://localhost:3000)

## 🏗️ Project Structure

```
src/
├── components/
│   ├── HotelRegistrationWizard.js    # Main registration wizard
│   ├── StepIndicator.js              # Progress indicator
│   ├── HotelBasicInfo.js             # Step 1: Basic information
│   ├── RoomManagement.js             # Step 2: Room management
│   ├── RegistrationSummary.js        # Step 3: Review & submit
│   ├── VerificationPanel.js          # Admin verification interface
│   └── RegisteredHotelsDisplay.js    # Public hotel listing
├── styles/
│   └── HotelRegistry.css             # Main styling
├── api.js                            # API integration
└── App.js                            # Main application component
```

## 🎨 Design System

### Color Palette
- **Primary**: `#2563eb` (Blue)
- **Secondary**: `#f8fafc` (Light Gray)
- **Success**: `#10b981` (Green)
- **Warning**: `#f59e0b` (Amber)
- **Danger**: `#ef4444` (Red)

### Components
- **Cards**: Elevated containers with rounded corners and shadows
- **Buttons**: Multiple variants (primary, secondary, success, danger)
- **Forms**: Clean inputs with validation states
- **Modals**: Overlay dialogs for detailed views
- **Progress Indicators**: Visual step tracking

## 🔧 Available Scripts

### `npm start`
Runs the app in development mode at [http://localhost:3000](http://localhost:3000)

### `npm test`
Launches the test runner in interactive watch mode

### `npm run build`
Builds the app for production to the `build` folder

### `npm run eject`
**Note: This is a one-way operation!** Ejects from Create React App

## 🌐 API Integration

The frontend integrates with the backend API for:

- **Hotel Registration**: `POST /hotels/register`
- **Hotel Verification**: `POST /hotels/verify/:id`
- **Get Hotels**: `GET /hotels`
- **Room Management**: `POST /rooms`, `GET /rooms/:hotelId`

## 📱 Responsive Design

The application is fully responsive and works seamlessly on:
- 📱 Mobile devices (320px+)
- 📱 Tablets (768px+)
- 💻 Desktop computers (1024px+)
- 🖥️ Large screens (1200px+)

## 🔒 Security Features

- Input validation and sanitization
- File upload restrictions (type and size)
- Wallet address validation
- HTTPS-ready for production deployment

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Static Hosting
The `build` folder can be deployed to any static hosting service:
- Netlify
- Vercel
- AWS S3 + CloudFront
- GitHub Pages

### Environment Variables for Production
```
REACT_APP_API_URL=https://your-api-domain.com/api
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

---

Built with ❤️ using React and modern web technologies
